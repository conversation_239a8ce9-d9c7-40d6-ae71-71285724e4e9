import { useState } from 'react';
// @mui
import { Apple, Google, Facebook, Visibility, VisibilityOff } from '@mui/icons-material';
import {
  Box,
  Link,
  Stack,
  Button,
  Divider,
  Container,
  TextField,
  Typography,
  IconButton,
  InputAdornment,
} from '@mui/material';
// routes
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/general';
// components
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

export default function LoginView() {
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <Container maxWidth="sm">
        <CustomCard
          sx={{
            maxWidth: 500,
            py: 8,
            px: 6,
          }}
        >
          <Stack spacing={3}>
            {/* Header */}
            <Typography
              variant="h3"
              sx={{
                mb: 2,
              }}
            >
              Login
            </Typography>

            {/* Email Field */}
            <Box>
              <Typography
                variant="subtitle2"
                sx={{
                  mb: 1,
                }}
              >
                Email
              </Typography>
              <TextField
                fullWidth
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </Box>

            {/* Password Field */}
            <Box>
              <Typography
                variant="subtitle2"
                sx={{
                  mb: 1,
                }}
              >
                Password
              </Typography>
              <TextField
                fullWidth
                type={showPassword ? 'text' : 'password'}
                placeholder="must be 8 characters"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePassword}
                        edge="end"
                        sx={{ color: 'rgba(255, 255, 255, 0.6)' }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            {/* Login Button */}
            <Stack spacing={1}>
              <Button fullWidth variant="contained" color="primary" size="large">
                Log in
              </Button>
              <Typography variant="caption">
                If don&apos;t have any account{' '}
                <Link
                  component={RouterLink}
                  href={paths.dashboard.register}
                  variant="subtitle2"
                  color="white"
                >
                  create an account
                </Link>
              </Typography>
            </Stack>

            {/* Divider */}
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 4, mb: 0 }}>
              <Divider sx={{ flex: 1, borderColor: 'rgba(255, 255, 255, 0.2)' }} />
              <Typography
                variant="body2"
                sx={{
                  color: 'rgba(255, 255, 255, 0.6)',
                  mx: 2,
                  fontSize: '0.875rem',
                }}
              >
                Or login with
              </Typography>
              <Divider sx={{ flex: 1, borderColor: 'rgba(255, 255, 255, 0.2)' }} />
            </Box>

            {/* Social Login Buttons */}
            <Stack spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                sx={{
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  color: '#ffffff',
                  border: 'none',
                  py: 1.5,
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: 'none',
                  },
                }}
              >
                Login with Google
              </Button>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple />}
                sx={{
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  color: '#ffffff',
                  border: 'none',
                  py: 1.5,
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: 'none',
                  },
                }}
              >
                Login with Apple
              </Button>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                sx={{
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  color: '#ffffff',
                  border: 'none',
                  py: 1.5,
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    border: 'none',
                  },
                }}
              >
                Login with Facebook
              </Button>
            </Stack>
          </Stack>
        </CustomCard>
      </Container>
    </Box>
  );
}

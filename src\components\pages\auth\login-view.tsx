import { Box, Grid, Container, Typography } from '@mui/material';
import { CustomCard } from 'src/components/shared/cards';

export default function LoginView() {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(180deg, #1a1d29 0%, #0f1117 100%)',
        py: 3,
      }}
    >
      <Container maxWidth="xl">
        <Grid container spacing={3} sx={{ minHeight: 'calc(100vh - 200px)' }}>
          <Grid item xs={12} md={6}>
            <CustomCard>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  minHeight: '500px',
                }}
              >
                login
              </Box>
            </CustomCard>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

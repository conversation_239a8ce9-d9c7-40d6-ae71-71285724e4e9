import { ReactNode } from 'react';
import { Card, CardContent, Typography, <PERSON>, Button, Chip } from '@mui/material';
import { alpha } from '@mui/material/styles';

interface InteractiveCardProps {
  title: string;
  description: string;
  images?: string[];
  actionButton?: ReactNode;
  chatBubble?: {
    text: string;
    position: 'left' | 'right';
  };
  variant?: 'default' | 'translation';
  sx?: object;
}

export default function InteractiveCard({ 
  title, 
  description, 
  images, 
  actionButton, 
  chatBubble,
  variant = 'default',
  sx = {} 
}: InteractiveCardProps) {
  return (
    <Card
      sx={{
        background: 'linear-gradient(135deg, #2a2d3a 0%, #1e2028 100%)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        height: '100%',
        minHeight: variant === 'translation' ? '200px' : '280px',
        position: 'relative',
        overflow: 'hidden',
        ...sx,
      }}
    >
      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Status indicator */}
        <Box
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            width: 8,
            height: 8,
            borderRadius: '50%',
            backgroundColor: '#6366f1',
            boxShadow: '0 0 8px rgba(99, 102, 241, 0.6)',
          }}
        />
        
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              color: '#ffffff',
              fontWeight: 600,
              fontSize: '1.125rem',
              lineHeight: 1.4,
              mb: 1,
            }}
          >
            {title}
          </Typography>
          
          <Typography
            variant="body2"
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '0.875rem',
              fontWeight: 400,
              lineHeight: 1.5,
            }}
          >
            {description}
          </Typography>
        </Box>
        
        {/* Interactive elements area */}
        <Box sx={{ flex: 1, position: 'relative', mt: 2 }}>
          {variant === 'default' && (
            <>
              {/* Chat interface elements */}
              <Box sx={{ position: 'relative', height: '100%' }}>
                {/* Translation chips */}
                <Box sx={{ position: 'absolute', top: 20, left: 20 }}>
                  <Chip
                    label="Translation"
                    size="small"
                    sx={{
                      backgroundColor: alpha('#6366f1', 0.2),
                      color: '#6366f1',
                      border: '1px solid rgba(99, 102, 241, 0.3)',
                      fontSize: '0.75rem',
                    }}
                  />
                </Box>
                
                {/* Chat bubble */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 60,
                    left: 20,
                    backgroundColor: '#6366f1',
                    borderRadius: '12px 12px 12px 4px',
                    padding: '8px 12px',
                    maxWidth: '140px',
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      color: '#ffffff',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                    }}
                  >
                    Open stomach
                  </Typography>
                </Box>
                
                {/* Language indicators */}
                <Box sx={{ position: 'absolute', bottom: 40, left: 20, display: 'flex', gap: 1 }}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      backgroundColor: alpha('#6366f1', 0.2),
                      border: '1px solid rgba(99, 102, 241, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="caption" sx={{ color: '#6366f1', fontSize: '0.7rem' }}>
                      EN
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      backgroundColor: alpha('#f59e0b', 0.2),
                      border: '1px solid rgba(245, 158, 11, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="caption" sx={{ color: '#f59e0b', fontSize: '0.7rem' }}>
                      AR
                    </Typography>
                  </Box>
                </Box>
                
                {/* Doctor images */}
                {images && images.length > 0 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      width: '60%',
                      height: '70%',
                      display: 'flex',
                      alignItems: 'flex-end',
                      justifyContent: 'flex-end',
                      gap: 1,
                    }}
                  >
                    {images.slice(0, 2).map((image, index) => (
                      <Box
                        key={index}
                        sx={{
                          width: index === 0 ? 60 : 50,
                          height: index === 0 ? 80 : 70,
                          borderRadius: '8px',
                          backgroundImage: `url(${image})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                          border: '2px solid rgba(255, 255, 255, 0.1)',
                          opacity: 0.8,
                        }}
                      />
                    ))}
                  </Box>
                )}
              </Box>
            </>
          )}
          
          {variant === 'translation' && (
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Typography
                variant="h6"
                sx={{
                  color: '#ffffff',
                  fontWeight: 600,
                  fontSize: '1rem',
                  mb: 1,
                }}
              >
                Real-Time Voice Translation
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  fontSize: '0.875rem',
                }}
              >
                Doctor-Patient Conversations
              </Typography>
            </Box>
          )}
        </Box>
        
        {actionButton && (
          <Box sx={{ mt: 2 }}>
            {actionButton}
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

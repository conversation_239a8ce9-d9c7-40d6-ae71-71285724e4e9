import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
// guard
// import { AuthGuard } from 'src/guard';
// layouts
import DashboardLayout from 'src/layouts/dashboard';
// components
import { LoadingScreen } from 'src/components/shared/loading-screen';

// ----------------------------------------------------------------------

const Home = lazy(() => import('src/pages/dashboard/home'));
const DiscoverPage = lazy(() => import('src/pages/dashboard/discover'));
const DoctorsPage = lazy(() => import('src/pages/dashboard/doctors'));
const MentalHealthPage = lazy(() => import('src/pages/dashboard/mental-health'));
const ServicesPage = lazy(() => import('src/pages/dashboard/services'));
const PlansPage = lazy(() => import('src/pages/dashboard/plans'));
const ContactUsPage = lazy(() => import('src/pages/dashboard/contact-us'));
const AboutUsPage = lazy(() => import('src/pages/dashboard/about-us'));
const BlogPage = lazy(() => import('src/pages/dashboard/blog'));
const LoginPage = lazy(() => import('src/pages/dashboard/login'));
const RegisterPage = lazy(() => import('src/pages/dashboard/register'));

// ----------------------------------------------------------------------

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: (
      <DashboardLayout>
        <Suspense fallback={<LoadingScreen />}>
          <Outlet />
        </Suspense>
      </DashboardLayout>
    ),
    children: [
      { element: <Home />, index: true },
      { path: 'discover', element: <DiscoverPage /> },
      { path: 'doctors', element: <DoctorsPage /> },
      { path: 'mental-health', element: <MentalHealthPage /> },
      { path: 'services', element: <ServicesPage /> },
      { path: 'plans', element: <PlansPage /> },
      { path: 'contact-us', element: <ContactUsPage /> },
      { path: 'about-us', element: <AboutUsPage /> },
      { path: 'blog', element: <BlogPage /> },
      // Auth routes in dashboard
      { path: 'login', element: <LoginPage /> },
      { path: 'register', element: <RegisterPage /> },
    ],
  },
];

import { memo, useState, useCallback } from 'react';
// @mui
import { Box } from '@mui/material';
import Stack from '@mui/material/Stack';
import Collapse from '@mui/material/Collapse';
import ListSubheader from '@mui/material/ListSubheader';
// components
import NavList from './nav-list';
import SvgColor from '../../svg-color';
import { NavProps, NavGroupProps } from '../types';

// ----------------------------------------------------------------------
const settingsItem = [
  {
    title: 'Light Mode',
    path: '#',
    icon: <SvgColor src="/assets/icons/navbar/ic_light_mode.svg" sx={{ width: 1, height: 1 }} />,
  },
  {
    title: 'Login',
    path: '#',
    icon: <SvgColor src="/assets/icons/navbar/ic_login.svg" sx={{ width: 1, height: 1 }} />,
  },
  {
    title: 'Updates & FAQ',
    path: '#',
    icon: <SvgColor src="/assets/icons/navbar/ic_updates.svg" sx={{ width: 1, height: 1 }} />,
  },
];
// ----------------------------------------------------------------------

function NavSectionVertical({ data, slotProps, ...other }: NavProps) {
  return (
    <Stack height={1} component="nav" id="nav-section-vertical" {...other}>
      {data.map((group, index) => (
        <Group
          key={group.subheader || index}
          subheader={group.subheader}
          items={group.items}
          slotProps={slotProps}
        />
      ))}
    </Stack>
  );
}

export default memo(NavSectionVertical);

// ----------------------------------------------------------------------

function Group({ subheader, items, slotProps }: NavGroupProps) {
  const [open, setOpen] = useState(true);

  const handleToggle = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const renderContent = items.map((list) => (
    <NavList key={list.title} data={list} depth={1} slotProps={slotProps} />
  ));

  const groupRootSx = {
    px: 2,
    pt: 5,
    ...(!subheader && { flexGrow: 1 }), // Allow this Stack to grow
  };

  return (
    <Stack sx={groupRootSx}>
      {subheader ? (
        <>
          <ListSubheader
            disableGutters
            disableSticky
            onClick={handleToggle}
            sx={{
              fontSize: 11,
              cursor: 'pointer',
              typography: 'overline',
              display: 'inline-flex',
              color: 'text.disabled',
              mb: `${slotProps?.gap || 4}px`,
              p: (theme) => theme.spacing(2, 1, 1, 1.5),
              transition: (theme) =>
                theme.transitions.create(['color'], {
                  duration: theme.transitions.duration.shortest,
                }),
              '&:hover': {
                color: 'text.primary',
              },
              ...slotProps?.subheader,
            }}
          >
            {subheader}
          </ListSubheader>

          <Collapse in={open}>{renderContent}</Collapse>
        </>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            flexGrow: 1,
            height: '100%',
            pb: 5,
          }}
        >
          <Box>{renderContent}</Box>
          <Box>
            {settingsItem.map((item) => (
              <NavList key={item.title} data={item} depth={1} slotProps={slotProps} />
            ))}
          </Box>
        </Box>
      )}
    </Stack>
  );
}

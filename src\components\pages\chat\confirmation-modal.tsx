import { Info as InfoIcon, Close as CloseIcon } from '@mui/icons-material';
import { Box, Dialog, Button, Typography, IconButton, DialogContent } from '@mui/material';
import { Doctor } from './type';

interface ConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  doctor: Doctor | null;
  onConfirm: () => void;
}

export default function ConfirmationModal({
  open,
  onClose,
  doctor,
  onConfirm,
}: ConfirmationModalProps) {
  if (!doctor) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: 'rgba(71, 85, 105, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(148, 163, 184, 0.3)',
          borderRadius: '16px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)',
        },
      }}
      BackdropProps={{
        sx: {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(4px)',
        },
      }}
    >
      <DialogContent
        sx={{
          p: 4,
          position: 'relative',
        }}
      >
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            color: 'rgba(255, 255, 255, 0.7)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              color: '#ffffff',
            },
          }}
        >
          <CloseIcon />
        </IconButton>

        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            mb: 3,
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px solid rgba(59, 130, 246, 0.3)',
            }}
          >
            <InfoIcon
              sx={{
                color: '#3b82f6',
                fontSize: '1.25rem',
              }}
            />
          </Box>
          <Typography
            variant="h6"
            sx={{
              color: '#ffffff',
              fontSize: '1.25rem',
              fontWeight: 600,
            }}
          >
            Alert
          </Typography>
        </Box>

        {/* Subtitle */}
        <Typography
          variant="body2"
          sx={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '0.875rem',
            mb: 3,
          }}
        >
          This is message !
        </Typography>

        {/* Main Content */}
        <Typography
          variant="body1"
          sx={{
            color: '#ffffff',
            fontSize: '0.95rem',
            lineHeight: 1.6,
            mb: 4,
          }}
        >
          By accepting these terms, I waive all legal claims against MediPath, understand the
          restrictions on non-licensed services, and confirm I am currently in [Selected Country].
        </Typography>

        {/* Action Buttons */}
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'flex-end',
          }}
        >
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              borderColor: 'rgba(255, 255, 255, 0.3)',
              backgroundColor: 'transparent',
              px: 3,
              py: 1,
              borderRadius: '8px',
              textTransform: 'none',
              fontSize: '0.875rem',
              fontWeight: 500,
              '&:hover': {
                borderColor: 'rgba(255, 255, 255, 0.5)',
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            variant="contained"
            sx={{
              backgroundColor: '#6366f1',
              color: '#ffffff',
              px: 3,
              py: 1,
              borderRadius: '8px',
              textTransform: 'none',
              fontSize: '0.875rem',
              fontWeight: 500,
              boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
              '&:hover': {
                backgroundColor: '#5855eb',
                boxShadow: '0 6px 16px rgba(99, 102, 241, 0.4)',
              },
            }}
          >
            Accept Terms
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
}

import * as Yup from 'yup';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import InputAdornment from '@mui/material/InputAdornment';
// routes
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/general';
// hooks
import { useRouter } from 'src/hooks/use-router';
import { useBoolean } from 'src/hooks/use-boolean';
import { useAuthContext } from 'src/hooks/use-auth-context';
import { useSearchParams } from 'src/hooks/use-search-params';
// config
import { PATH_AFTER_LOGIN } from 'src/config-global';
// components
import Iconify from 'src/components/shared/iconify';
import { CustomCard } from 'src/components/shared/cards';
import FormProvider, { RHFTextField } from 'src/components/shared/hook-form';

// ----------------------------------------------------------------------

export default function PatientLoginView() {
  const { login } = useAuthContext();
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo');
  const password = useBoolean();

  const LoginSchema = Yup.object().shape({
    email: Yup.string().required('Email is required').email('Email must be a valid email address'),
    password: Yup.string().required('Password is required'),
  });

  const defaultValues = {
    email: '',
    password: '',
  };

  const methods = useForm({
    resolver: yupResolver(LoginSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await login?.(data.email, data.password);
      router.push(returnTo || PATH_AFTER_LOGIN);
    } catch (error) {
      console.error(error);
      reset();
      setErrorMsg(typeof error === 'string' ? error : error.message);
    }
  });

  const renderHeader = (
    <Stack spacing={3} sx={{ mb: 4, textAlign: 'center' }}>
      <Box
        sx={{
          width: 80,
          height: 80,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'primary.main',
          color: 'white',
          mx: 'auto',
        }}
      >
        <Iconify icon="solar:user-heart-rounded-bold" width={40} height={40} />
      </Box>

      <Stack spacing={1}>
        <Typography variant="h4" color="text.primary">
          Patient Portal
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Access your health records and manage appointments
        </Typography>
      </Stack>

      <Stack direction="row" spacing={0.5} justifyContent="center">
        <Typography variant="body2" color="text.secondary">
          New patient?
        </Typography>
        <Link component={RouterLink} href={`${paths.auth.patient.register}`} variant="subtitle2">
          Create account
        </Link>
      </Stack>
    </Stack>
  );

  const renderForm = (
    <Stack spacing={3}>
      {!!errorMsg && <Alert severity="error">{errorMsg}</Alert>}

      <RHFTextField
        name="email"
        label="Email address"
        placeholder="Enter your email"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="eva:email-outline" />
            </InputAdornment>
          ),
        }}
      />

      <RHFTextField
        name="password"
        label="Password"
        placeholder="Enter your password"
        type={password.value ? 'text' : 'password'}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="eva:lock-outline" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={password.onToggle} edge="end">
                <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      <Link variant="body2" color="inherit" underline="always" sx={{ alignSelf: 'flex-end' }}>
        Forgot password?
      </Link>

      <LoadingButton
        fullWidth
        color="primary"
        size="large"
        type="submit"
        variant="contained"
        loading={isSubmitting}
        sx={{
          py: 1.5,
          fontSize: '1.1rem',
          fontWeight: 600,
        }}
      >
        Sign In
      </LoadingButton>
    </Stack>
  );

  return (
    <CustomCard
      sx={{
        maxWidth: 480,
        mx: 'auto',
        p: 4,
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        {renderHeader}
        {renderForm}
      </FormProvider>
    </CustomCard>
  );
}

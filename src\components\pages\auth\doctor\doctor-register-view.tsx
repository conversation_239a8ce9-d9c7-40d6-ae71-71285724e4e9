import * as Yup from 'yup';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import InputAdornment from '@mui/material/InputAdornment';
// routes
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/general';
// hooks
import { useRouter } from 'src/hooks/use-router';
import { useBoolean } from 'src/hooks/use-boolean';
import { useAuthContext } from 'src/hooks/use-auth-context';
import { useSearchParams } from 'src/hooks/use-search-params';
// config
import { PATH_AFTER_LOGIN } from 'src/config-global';
// components
import Iconify from 'src/components/shared/iconify';
import { CustomCard } from 'src/components/shared/cards';
import FormProvider, { RHFTextField } from 'src/components/shared/hook-form';

// ----------------------------------------------------------------------

const SPECIALTIES = [
  'General Practice',
  'Internal Medicine',
  'Pediatrics',
  'Cardiology',
  'Dermatology',
  'Neurology',
  'Orthopedics',
  'Psychiatry',
  'Radiology',
  'Surgery',
  'Gynecology',
  'Ophthalmology',
  'ENT',
  'Anesthesiology',
  'Emergency Medicine',
  'Other',
];

export default function DoctorRegisterView() {
  const { register } = useAuthContext();
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo');
  const password = useBoolean();

  const RegisterSchema = Yup.object().shape({
    firstName: Yup.string().required('First name is required'),
    lastName: Yup.string().required('Last name is required'),
    email: Yup.string().required('Email is required').email('Email must be a valid email address'),
    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirmPassword: Yup.string()
      .required('Please confirm your password')
      .oneOf([Yup.ref('password')], 'Passwords must match'),
    phoneNumber: Yup.string().required('Phone number is required'),
    licenseNumber: Yup.string().required('Medical license number is required'),
    specialty: Yup.string().required('Medical specialty is required'),
    hospitalAffiliation: Yup.string().required('Hospital/Clinic affiliation is required'),
  });

  const defaultValues = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    licenseNumber: '',
    specialty: '',
    hospitalAffiliation: '',
  };

  const methods = useForm({
    resolver: yupResolver(RegisterSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await register?.(data.email, data.password, data.firstName, data.lastName, 'doctor');
      router.push(returnTo || PATH_AFTER_LOGIN);
    } catch (error) {
      console.error(error);
      reset();
      setErrorMsg(typeof error === 'string' ? error : error.message);
    }
  });

  const renderHeader = (
    <Stack spacing={3} sx={{ mb: 4, textAlign: 'center' }}>
      <Box
        sx={{
          width: 80,
          height: 80,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'secondary.main',
          color: 'white',
          mx: 'auto',
        }}
      >
        <Iconify icon="solar:stethoscope-bold" width={40} height={40} />
      </Box>

      <Stack spacing={1}>
        <Typography variant="h4" color="text.primary">
          Join as Doctor
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Create your professional account to manage your practice
        </Typography>
      </Stack>

      <Stack direction="row" spacing={0.5} justifyContent="center">
        <Typography variant="body2" color="text.secondary">
          Already have an account?
        </Typography>
        <Link component={RouterLink} href={`${paths.auth.doctor.login}`} variant="subtitle2">
          Sign in
        </Link>
      </Stack>
    </Stack>
  );

  return (
    <CustomCard
      blueCard
      sx={{
        maxWidth: 600,
        mx: 'auto',
        p: 4,
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        {renderHeader}

        <Stack spacing={3}>
          {!!errorMsg && <Alert severity="error">{errorMsg}</Alert>}

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <RHFTextField name="firstName" label="First Name" placeholder="Enter your first name" />
            <RHFTextField name="lastName" label="Last Name" placeholder="Enter your last name" />
          </Stack>

          <RHFTextField
            name="email"
            label="Professional Email"
            placeholder="Enter your professional email"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:email-outline" />
                </InputAdornment>
              ),
            }}
          />

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <RHFTextField
              name="phoneNumber"
              label="Phone Number"
              placeholder="Enter your phone number"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:phone-outline" />
                  </InputAdornment>
                ),
              }}
            />
            <RHFTextField
              name="licenseNumber"
              label="Medical License"
              placeholder="Enter license number"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:file-text-outline" />
                  </InputAdornment>
                ),
              }}
            />
          </Stack>

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <RHFTextField
              name="specialty"
              label="Medical Specialty"
              select
              SelectProps={{
                native: false,
              }}
            >
              {SPECIALTIES.map((specialty) => (
                <MenuItem key={specialty} value={specialty}>
                  {specialty}
                </MenuItem>
              ))}
            </RHFTextField>
            <RHFTextField
              name="hospitalAffiliation"
              label="Hospital/Clinic"
              placeholder="Enter your affiliation"
            />
          </Stack>

          <RHFTextField
            name="password"
            label="Password"
            placeholder="Create a strong password"
            type={password.value ? 'text' : 'password'}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:lock-outline" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={password.onToggle} edge="end">
                    <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <RHFTextField
            name="confirmPassword"
            label="Confirm Password"
            placeholder="Confirm your password"
            type={password.value ? 'text' : 'password'}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:lock-outline" />
                </InputAdornment>
              ),
            }}
          />

          <LoadingButton
            fullWidth
            color="secondary"
            size="large"
            type="submit"
            variant="contained"
            loading={isSubmitting}
            sx={{
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
            }}
          >
            Create Doctor Account
          </LoadingButton>
        </Stack>
      </FormProvider>
    </CustomCard>
  );
}

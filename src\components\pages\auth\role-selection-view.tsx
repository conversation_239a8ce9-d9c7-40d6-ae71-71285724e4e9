import { useState } from 'react';
// @mui
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';
// routes
import { paths } from 'src/routes/paths';
import { useRouter } from 'src/hooks/use-router';
import Iconify from 'src/components/shared/iconify';
// components
import { CustomCard } from 'src/components/shared/cards';

// ----------------------------------------------------------------------

type UserRole = 'patient' | 'doctor';

interface RoleCardProps {
  title: string;
  description: string;
  icon: string;
  selected: boolean;
  onClick: () => void;
}

function RoleCard({ title, description, icon, selected, onClick }: RoleCardProps) {
  const theme = useTheme();

  return (
    <CustomCard
      sx={{
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        border: selected ? `2px solid ${theme.palette.primary.main}` : '2px solid transparent',
        transform: selected ? 'scale(1.02)' : 'scale(1)',
        '&:hover': {
          transform: 'scale(1.02)',
          border: `2px solid ${alpha(theme.palette.primary.main, 0.5)}`,
        },
      }}
    >
      <Stack
        spacing={3}
        alignItems="center"
        justifyContent="center"
        sx={{
          p: 4,
          minHeight: 280,
          textAlign: 'center',
        }}
        onClick={onClick}
        tabIndex={0}
      >
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: alpha(theme.palette.primary.main, 0.1),
            color: theme.palette.primary.main,
          }}
        >
          <Iconify icon={icon} width={40} height={40} />
        </Box>

        <Stack spacing={1}>
          <Typography variant="h5" color="text.primary">
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {description}
          </Typography>
        </Stack>

        {selected && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              width: 24,
              height: 24,
              borderRadius: '50%',
              bgcolor: theme.palette.primary.main,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Iconify icon="eva:checkmark-fill" width={16} height={16} color="white" />
          </Box>
        )}
      </Stack>
    </CustomCard>
  );
}

// ----------------------------------------------------------------------

export default function RoleSelectionView() {
  const router = useRouter();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
  };

  const handleContinue = () => {
    if (selectedRole) {
      router.push(`${paths.auth.jwt.register}?role=${selectedRole}`);
    }
  };

  const handleLogin = () => {
    router.push(paths.auth.jwt.login);
  };

  // Determine the label for the selected role
  let roleLabel = '...';
  if (selectedRole === 'patient') {
    roleLabel = 'Patient';
  } else if (selectedRole === 'doctor') {
    roleLabel = 'Doctor';
  }

  return (
    <Stack spacing={5} sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Header */}
      <Stack spacing={2} sx={{ textAlign: 'center' }}>
        <Typography variant="h3" color="text.primary">
          Join MediPath
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Choose your role to get started with personalized healthcare experience
        </Typography>
      </Stack>

      {/* Role Cards */}
      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ width: '100%' }}>
        <RoleCard
          title="I'm a Patient"
          description="Book appointments, manage health records, and connect with healthcare providers"
          icon="solar:user-heart-rounded-bold"
          selected={selectedRole === 'patient'}
          onClick={() => handleRoleSelect('patient')}
        />

        <RoleCard
          title="I'm a Doctor"
          description="Manage appointments, patient records, and provide quality healthcare services"
          icon="solar:stethoscope-bold"
          selected={selectedRole === 'doctor'}
          onClick={() => handleRoleSelect('doctor')}
        />
      </Stack>

      {/* Action Buttons */}
      <Stack spacing={2}>
        <Button
          fullWidth
          size="large"
          variant="contained"
          disabled={!selectedRole}
          onClick={handleContinue}
          sx={{
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 600,
          }}
        >
          Continue as {roleLabel}
        </Button>

        <Stack direction="row" spacing={0.5} justifyContent="center">
          <Typography variant="body2" color="text.secondary">
            Already have an account?
          </Typography>
          <Button
            variant="text"
            onClick={handleLogin}
            sx={{
              p: 0,
              minWidth: 'auto',
              textDecoration: 'underline',
              '&:hover': {
                textDecoration: 'underline',
                bgcolor: 'transparent',
              },
            }}
          >
            Sign in
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

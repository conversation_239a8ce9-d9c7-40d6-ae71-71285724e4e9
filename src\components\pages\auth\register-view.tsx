import { Box, Grid, Container, Typography } from '@mui/material';
import { CustomCard } from 'src/components/shared/cards';

export default function RegisterView() {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(180deg, #1a1d29 0%, #0f1117 100%)',
        py: 3,
      }}
    >
      <Container maxWidth="xl">
        <Grid container spacing={3} sx={{ minHeight: 'calc(100vh - 200px)' }}>
          {/* Left side - Hero Card */}
          <Grid item xs={12} md={6}>
            <CustomCard>
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h3" sx={{ color: '#fff', mb: 1 }}>
                  Join Our Network of Healthcare Professionals
                </Typography>
                <Typography variant="subtitle1" sx={{ color: 'rgba(255,255,255,0.7)' }}>
                  Connect with patients and enhance your practice with AI-powered tools
                </Typography>
              </Box>
            </CustomCard>
          </Grid>

          {/* Right side - Register Form */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                minHeight: '500px',
              }}
            >
              register
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

import { ReactNode } from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';

interface FeatureCardProps {
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  icon?: ReactNode;
  variant?: 'default' | 'highlighted' | 'insurance';
  sx?: object;
}

export default function FeatureCard({ 
  title, 
  subtitle, 
  description, 
  image, 
  icon, 
  variant = 'default',
  sx = {} 
}: FeatureCardProps) {
  const getCardStyles = () => {
    switch (variant) {
      case 'highlighted':
        return {
          background: 'linear-gradient(135deg, #2a2d3a 0%, #1e2028 100%)',
          border: '1px solid rgba(99, 102, 241, 0.3)',
        };
      case 'insurance':
        return {
          background: 'linear-gradient(135deg, #2a2d3a 0%, #1e2028 100%)',
          border: '1px solid rgba(245, 158, 11, 0.3)',
        };
      default:
        return {
          background: 'linear-gradient(135deg, #2a2d3a 0%, #1e2028 100%)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        };
    }
  };

  return (
    <Card
      sx={{
        ...getCardStyles(),
        borderRadius: '16px',
        height: '100%',
        minHeight: '140px',
        position: 'relative',
        overflow: 'hidden',
        ...sx,
      }}
    >
      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
        <Box>
          <Typography
            variant="h6"
            sx={{
              color: '#ffffff',
              fontWeight: 600,
              fontSize: '1.125rem',
              lineHeight: 1.4,
              mb: subtitle ? 0.5 : 1,
            }}
          >
            {title}
          </Typography>
          
          {subtitle && (
            <Typography
              variant="body2"
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '0.875rem',
                fontWeight: 400,
                mb: 1,
              }}
            >
              {subtitle}
            </Typography>
          )}
          
          {description && (
            <Typography
              variant="body2"
              sx={{
                color: 'rgba(255, 255, 255, 0.6)',
                fontSize: '0.8rem',
                fontWeight: 400,
                lineHeight: 1.5,
              }}
            >
              {description}
            </Typography>
          )}
        </Box>
        
        {image && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              bottom: 0,
              width: '40%',
              backgroundImage: `url(${image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.3,
              zIndex: 1,
            }}
          />
        )}
        
        {icon && (
          <Box sx={{ position: 'absolute', top: 16, right: 16, opacity: 0.4, zIndex: 2 }}>
            {icon}
          </Box>
        )}
        
        {variant === 'highlighted' && (
          <Box
            sx={{
              position: 'absolute',
              top: 12,
              right: 12,
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: '#6366f1',
              boxShadow: '0 0 8px rgba(99, 102, 241, 0.6)',
            }}
          />
        )}
        
        {variant === 'insurance' && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              right: 16,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                background: 'conic-gradient(from 0deg, #f59e0b, #fbbf24, #f59e0b)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  inset: 2,
                  borderRadius: '50%',
                  background: '#2a2d3a',
                },
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  color: '#f59e0b',
                  fontWeight: 700,
                  fontSize: '0.75rem',
                  position: 'relative',
                  zIndex: 1,
                }}
              >
                45
              </Typography>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

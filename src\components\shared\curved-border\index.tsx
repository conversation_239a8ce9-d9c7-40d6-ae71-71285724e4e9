import { m } from 'framer-motion';
import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Box } from '@mui/material';
import Image from '../image';

interface CurvedBorderProps {
  height: number;
}

export default function CurvedBorder({ height }: CurvedBorderProps) {
  const { pathname } = useLocation();
  const [activeItemPosition, setActiveItemPosition] = useState<number>(0);

  const ANIMATION_DURATION_S = 0.4; // Consistent animation duration in ms

  useEffect(() => {
    // Find active nav item and get its position
    const findActiveItem = () => {
      const activeItem = document.querySelector('.nav-item.active') as HTMLElement;
      const navContainer = document.querySelector('[data-nav-container]') as HTMLElement;

      if (activeItem && navContainer) {
        const rect = activeItem.getBoundingClientRect();
        const navRect = navContainer.getBoundingClientRect();
        const relativeTop = rect.top - navRect.top + rect.height / 2;
        setActiveItemPosition(relativeTop);
      } else {
        setActiveItemPosition(0);
      }
    };

    // Initial check
    findActiveItem();

    // Check again after multiple delays to ensure DOM is fully updated
    const timer1 = setTimeout(findActiveItem, 50);
    const timer2 = setTimeout(findActiveItem, 200);
    const timer3 = setTimeout(findActiveItem, 500);

    // Also listen for resize events
    const handleResize = () => findActiveItem();
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      window.removeEventListener('resize', handleResize);
    };
  }, [pathname]);

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: 60, // Make wider to accommodate S-curve
        height: '100%',
        pointerEvents: 'none',
        zIndex: 1000,
        transition: `all ${ANIMATION_DURATION_S}s ease-in-out`,
      }}
    >
      <svg
        width="60"
        height={height}
        style={{
          position: 'absolute',
          right: 0,
          top: 0,
          transition: `opacity ${ANIMATION_DURATION_S}s ease-in-out`,
          opacity: 1,
        }}
        viewBox={`-40 0 60 ${height}`} // Extend viewBox to the left for S-curve
      >
        {/* Show S-curved  border if active item exists */}
        {/* Top straight part */}
        <m.line
          x1="20"
          y1="0"
          x2="20"
          stroke="rgba(255, 255, 255, 0.15)"
          strokeWidth="2"
          animate={{ y2: activeItemPosition > 0 ? activeItemPosition - 60 : 0 }}
          transition={{ duration: ANIMATION_DURATION_S, ease: 'easeInOut' }}
        />

        {/* S-curve part - like in the image */}
        <m.path
          stroke="rgba(255, 255, 255, 0.15)"
          strokeWidth="2"
          fill="none"
          animate={{
            d:
              activeItemPosition > 0
                ? `M 20 ${activeItemPosition - 60}
                     C 20 ${activeItemPosition - 30}, -5 ${
                       activeItemPosition - 20
                     }, -5 ${activeItemPosition}
                     C -5 ${activeItemPosition + 20}, 20 ${activeItemPosition + 40}, 20 ${
                       activeItemPosition + 60
                     }`
                : `M 20 0 C 20 0, -5 0, -5 0 C -5 0, 20 0, 20 0`, // Default path when no active item
          }}
          transition={{ duration: ANIMATION_DURATION_S, ease: 'easeInOut' }}
        />

        {/* Bottom straight part */}
        <m.line
          x1="20"
          x2="20"
          y2={height}
          stroke="rgba(255, 255, 255, 0.15)"
          strokeWidth="2"
          animate={{ y1: activeItemPosition > 0 ? activeItemPosition + 60 : 0 }}
          transition={{ duration: ANIMATION_DURATION_S, ease: 'easeInOut' }}
        />
      </svg>

      {/* Active indicator dot - positioned on the RIGHT side */}
      <Image
        src="/assets/icons/navbar/bullet_menu_active.svg"
        sx={{
          position: 'absolute',
          top: activeItemPosition,
          right: -25, // Position the dot OUTSIDE the border, on the right side
          transform: 'translateY(-50%)',
          zIndex: 1001,
          transition: `top ${ANIMATION_DURATION_S}s ease-in-out`,
        }}
      />
    </Box>
  );
}

import { useMemo } from 'react';
// components
import SvgColor from 'src/components/shared/svg-color';

// ----------------------------------------------------------------------

const icon = (name: string) => (
  <SvgColor src={`/assets/icons/navbar/${name}.svg`} sx={{ width: 1, height: 1 }} />
);

const ICONS = {
  home: icon('ic_home'),
  discover: icon('ic_discover'),
  doctors: icon('ic_doctors'),
  mentalHealth: icon('ic_mental_health'),
  services: icon('ic_services'),
  plans: icon('ic_plans'),
  contactUs: icon('ic_contact_us'),
  aboutUs: icon('ic_about_us'),
  blog: icon('ic_blog'),
};

// ----------------------------------------------------------------------

export function useNavData() {
  const data = useMemo(
    () => [
      // MAIN NAVIGATION
      // ----------------------------------------------------------------------
      {
        subheader: '',
        items: [
          { title: 'Home', path: '/dashboard', icon: ICONS.home },
          { title: 'Discover', path: '/dashboard/discover', icon: ICONS.discover },
          { title: 'Doctors', path: '/dashboard/doctors', icon: ICONS.doctors },
          { title: 'Mental Health', path: '/dashboard/mental-health', icon: ICONS.mentalHealth },
          { title: 'Services', path: '/dashboard/services', icon: ICONS.services },
          { title: 'Plans', path: '/dashboard/plans', icon: ICONS.plans },
          { title: 'Contact Us', path: '/dashboard/contact-us', icon: ICONS.contactUs },
          { title: 'About Us', path: '/dashboard/about-us', icon: ICONS.aboutUs },
          { title: 'Blog', path: '/dashboard/blog', icon: ICONS.blog },
        ],
      },
    ],
    []
  );

  return data;
}

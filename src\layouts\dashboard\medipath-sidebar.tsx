import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Box, Drawer, Stack, Typography } from '@mui/material';
import { useResponsive } from 'src/hooks/use-responsive';
import { NAV } from '../config-layout';
import { MediPathNavItem } from './medipath-nav-item';

// Icons
import HomeIcon from '@mui/icons-material/Home';
import ExploreIcon from '@mui/icons-material/Explore';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import PsychologyIcon from '@mui/icons-material/Psychology';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import EventNoteIcon from '@mui/icons-material/EventNote';
import ContactMailIcon from '@mui/icons-material/ContactMail';
import InfoIcon from '@mui/icons-material/Info';
import ArticleIcon from '@mui/icons-material/Article';
import LightModeIcon from '@mui/icons-material/LightMode';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import HelpIcon from '@mui/icons-material/Help';
import LogoutIcon from '@mui/icons-material/Logout';

const navConfig = [
  {
    title: 'Home',
    path: '/dashboard/medipath',
    icon: <HomeIcon />,
  },
  {
    title: 'Discover',
    path: '/dashboard/medipath/discover',
    icon: <ExploreIcon />,
  },
  {
    title: 'Doctors',
    path: '/dashboard/medipath/doctors',
    icon: <LocalHospitalIcon />,
  },
  {
    title: 'Mental Health',
    path: '/dashboard/medipath/mental-health',
    icon: <PsychologyIcon />,
  },
  {
    title: 'Services',
    path: '/dashboard/medipath/services',
    icon: <MedicalServicesIcon />,
  },
  {
    title: 'Plans',
    path: '/dashboard/medipath/plans',
    icon: <EventNoteIcon />,
  },
  {
    title: 'Contact Us',
    path: '/dashboard/medipath/contact',
    icon: <ContactMailIcon />,
  },
  {
    title: 'About us',
    path: '/dashboard/medipath/about',
    icon: <InfoIcon />,
  },
  {
    title: 'Blog',
    path: '/dashboard/medipath/blog',
    icon: <ArticleIcon />,
  },
];

const bottomNavConfig = [
  {
    title: 'Light Mode',
    path: '#',
    icon: <LightModeIcon />,
  },
  {
    title: 'Login',
    path: '/auth/login',
    icon: <LoginIcon />,
  },
  {
    title: 'Sign Up',
    path: '/auth/register',
    icon: <PersonAddIcon />,
  },
  {
    title: 'Updates & FAQ',
    path: '/dashboard/medipath/faq',
    icon: <HelpIcon />,
  },
  {
    title: 'Log out',
    path: '/auth/logout',
    icon: <LogoutIcon />,
  },
];

type Props = {
  openNav: boolean;
  onCloseNav: VoidFunction;
};

export default function MediPathSidebar({ openNav, onCloseNav }: Props) {
  const { pathname } = useLocation();
  const lgUp = useResponsive('up', 'lg');

  useEffect(() => {
    if (openNav) {
      onCloseNav();
    }
  }, [pathname]);

  const renderContent = (
    <Box
      sx={{
        height: 1,
        background: 'linear-gradient(180deg, #1a1d29 0%, #0f1117 100%)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Logo */}
      <Box sx={{ px: 2.5, py: 3, display: 'flex', alignItems: 'center', gap: 1.5 }}>
        <Box
          sx={{
            width: 32,
            height: 32,
            borderRadius: '8px',
            background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: '#ffffff',
              fontWeight: 700,
              fontSize: '1rem',
            }}
          >
            M
          </Typography>
        </Box>
        <Typography
          variant="h6"
          sx={{
            color: '#ffffff',
            fontWeight: 600,
            fontSize: '1.125rem',
          }}
        >
          MediPath
        </Typography>
        <Box
          sx={{
            backgroundColor: '#6366f1',
            color: '#ffffff',
            borderRadius: '4px',
            px: 1,
            py: 0.25,
            fontSize: '0.75rem',
            fontWeight: 600,
          }}
        >
          AI
        </Box>
      </Box>

      {/* Navigation */}
      <Stack component="nav" spacing={0.5} sx={{ px: 2, flex: 1 }}>
        {navConfig.map((item) => (
          <MediPathNavItem key={item.title} item={item} />
        ))}
      </Stack>

      {/* Bottom Navigation */}
      <Stack component="nav" spacing={0.5} sx={{ px: 2, pb: 2 }}>
        {bottomNavConfig.map((item) => (
          <MediPathNavItem key={item.title} item={item} />
        ))}
      </Stack>
    </Box>
  );

  return (
    <Box
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV.W_VERTICAL },
      }}
    >
      {lgUp ? (
        <Box
          sx={{
            height: 1,
            position: 'fixed',
            width: NAV.W_VERTICAL,
            borderRight: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          {renderContent}
        </Box>
      ) : (
        <Drawer
          open={openNav}
          onClose={onCloseNav}
          PaperProps={{
            sx: {
              width: NAV.W_VERTICAL,
              border: 'none',
            },
          }}
        >
          {renderContent}
        </Drawer>
      )}
    </Box>
  );
}

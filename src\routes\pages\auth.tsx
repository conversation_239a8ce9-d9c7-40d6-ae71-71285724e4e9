import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
// layout
import AuthClassicLayout from 'src/layouts/auth/classic';
// components
import { SplashScreen } from 'src/components/shared/loading-screen';

// ----------------------------------------------------------------------

// JWT
const JwtLoginPage = lazy(() => import('src/pages/auth/login'));
const JwtRegisterPage = lazy(() => import('src/pages/auth/register'));

// Role Selection
const RoleSelectionPage = lazy(() => import('src/pages/auth/role-selection'));

// Patient Auth
const PatientLoginPage = lazy(() => import('src/pages/auth/patient-login'));
const PatientRegisterPage = lazy(() => import('src/pages/auth/patient-register'));

// Doctor Auth
const DoctorLoginPage = lazy(() => import('src/pages/auth/doctor-login'));
const DoctorRegisterPage = lazy(() => import('src/pages/auth/doctor-register'));

// ----------------------------------------------------------------------

const authJwt = {
  path: 'jwt',
  element: (
    <Suspense fallback={<SplashScreen />}>
      <Outlet />
    </Suspense>
  ),
  children: [
    {
      path: 'login',
      element: (
        <AuthClassicLayout>
          <JwtLoginPage />
        </AuthClassicLayout>
      ),
    },
    {
      path: 'register',
      element: (
        <AuthClassicLayout title="Manage the job more effectively with Minimal">
          <JwtRegisterPage />
        </AuthClassicLayout>
      ),
    },
  ],
};

// Role Selection Route
const roleSelection = {
  path: 'role-selection',
  element: (
    <Suspense fallback={<SplashScreen />}>
      <AuthClassicLayout title="Choose your role to get started">
        <RoleSelectionPage />
      </AuthClassicLayout>
    </Suspense>
  ),
};

// Patient Auth Routes
const patientAuth = {
  path: 'patient',
  element: (
    <Suspense fallback={<SplashScreen />}>
      <Outlet />
    </Suspense>
  ),
  children: [
    {
      path: 'login',
      element: (
        <AuthClassicLayout title="Welcome back to your health journey">
          <PatientLoginPage />
        </AuthClassicLayout>
      ),
    },
    {
      path: 'register',
      element: (
        <AuthClassicLayout title="Start your personalized healthcare journey">
          <PatientRegisterPage />
        </AuthClassicLayout>
      ),
    },
  ],
};

// Doctor Auth Routes
const doctorAuth = {
  path: 'doctor',
  element: (
    <Suspense fallback={<SplashScreen />}>
      <Outlet />
    </Suspense>
  ),
  children: [
    {
      path: 'login',
      element: (
        <AuthClassicLayout title="Access your professional practice tools">
          <DoctorLoginPage />
        </AuthClassicLayout>
      ),
    },
    {
      path: 'register',
      element: (
        <AuthClassicLayout title="Join our network of healthcare professionals">
          <DoctorRegisterPage />
        </AuthClassicLayout>
      ),
    },
  ],
};

export const authRoutes = [
  {
    path: 'auth',
    children: [authJwt, roleSelection, patientAuth, doctorAuth],
  },
];

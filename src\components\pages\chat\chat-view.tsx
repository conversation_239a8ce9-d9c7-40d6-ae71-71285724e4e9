// routes
import { useSearchParams } from 'react-router-dom';
// react
import { useRef, useState, useEffect } from 'react';
// @mui
import { Send as SendIcon, Close as CloseIcon } from '@mui/icons-material';
import { Box, Paper, Avatar, TextField, IconButton, Typography } from '@mui/material';
// components
import { ChatMessage, ChatViewProps } from './type';

// ----------------------------------------------------------------------

export default function ChatView({ initialMessage = '' }: ChatViewProps) {
  const [searchParams] = useSearchParams();
  const messageFromUrl = searchParams.get('message') || initialMessage;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: "They come and go. But when they hit, they're pretty intense.",
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '2',
      text: "Sorry to hear that. Let's get to the bottom of it. Are the headaches constant, or do they come and go?",
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '3',
      text: "They come and go. But when they hit, they're pretty intense.",
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '4',
      text: 'Okay, understood. Where exactly do you feel the pain? Is it in one specific area or all over your head?',
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '5',
      text: "They come and go. But when they hit, they're pretty intense.",
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '6',
      text: 'Based on your symptoms — throbbing pain around the temples and eyes, nausea, and light sensitivity — it sounds like you could be experiencing migraines. I recommend consulting a Neurologist to further assess and manage.',
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '6',
      text: 'Based on your symptoms — throbbing pain around the temples and eyes, nausea, and light sensitivity — it sounds like you could be experiencing migraines. I recommend consulting a Neurologist to further assess and manage.',
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '6',
      text: 'Based on your symptoms — throbbing pain around the temples and eyes, nausea, and light sensitivity — it sounds like you could be experiencing migraines. I recommend consulting a Neurologist to further assess and manage.',
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '6',
      text: 'Based on your symptoms — throbbing pain around the temples and eyes, nausea, and light sensitivity — it sounds like you could be experiencing migraines. I recommend consulting a Neurologist to further assess and manage.',
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
  ]);
  const [newMessage, setNewMessage] = useState('');

  // Set initial message from URL when component mounts
  useEffect(() => {
    if (messageFromUrl) {
      setNewMessage(messageFromUrl);
    }
  }, [messageFromUrl]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message: ChatMessage = {
        id: Date.now().toString(),
        text: newMessage,
        isUser: true,
        timestamp: new Date().toLocaleString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
          day: 'numeric',
          month: 'short',
        }),
      };
      setMessages((prev) => [...prev, message]);
      setNewMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        overflow: 'hidden',
        backgroundColor: '#03061C',
      }}
    >
      {/* Header */}
      {onClose && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 3,
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
            flexShrink: 0,
          }}
        >
          <Typography
            variant="h5"
            sx={{
              color: '#ffffff',
              fontWeight: 600,
            }}
          >
            AI Health Assistant
          </Typography>
          <IconButton
            onClick={onClose}
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              '&:hover': {
                color: '#ffffff',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      )}

      {/* Messages Container */}
      <Box
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 3,
          pb: 0, // Remove bottom padding to make room for fixed input
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
          minHeight: 0, // Important for flex child to be scrollable
          maxHeight: '60vh',
        }}
      >
        {messages.map((message) => (
          <Box
            key={message.id}
            sx={{
              display: 'flex',
              flexDirection: message.isUser ? 'row-reverse' : 'row',
              alignItems: 'flex-start',
              gap: 2,
            }}
          >
            {/* Avatar */}
            <Avatar
              sx={{
                width: 32,
                height: 32,
                backgroundColor: message.isUser ? '#6366f1' : 'rgba(255, 255, 255, 0.1)',
                fontSize: '0.875rem',
              }}
            >
              {message.isUser ? 'U' : 'AI'}
            </Avatar>

            {/* Message Content */}
            <Box
              sx={{
                maxWidth: '70%',
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
              }}
            >
              {/* Timestamp */}
              <Typography
                variant="caption"
                sx={{
                  color: 'rgba(255, 255, 255, 0.6)',
                  fontSize: '0.75rem',
                  textAlign: message.isUser ? 'right' : 'left',
                }}
              >
                {message.timestamp}
              </Typography>

              {/* Message Bubble */}
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: message.isUser
                    ? 'rgba(99, 102, 241, 0.2)'
                    : 'rgba(71, 85, 105, 0.3)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(148, 163, 184, 0.2)',
                  borderRadius: '16px',
                  borderTopLeftRadius: message.isUser ? '16px' : '4px',
                  borderTopRightRadius: message.isUser ? '4px' : '16px',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: '#ffffff',
                    lineHeight: 1.5,
                  }}
                >
                  {message.text}
                </Typography>
              </Paper>
            </Box>
          </Box>
        ))}

        {/* Extra padding at bottom to ensure last message is visible above input */}
        <Box sx={{ height: '20px' }} />
        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} />
      </Box>

      {/* Input Area - Fixed at bottom */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 80,

          p: 3,
          borderTop: '1px solid rgba(255, 255, 255, 0.1)',
          flexShrink: 0, // Prevent input area from shrinking
          backgroundColor: '#03061C', // Match dashboard background
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-end',
            gap: 2,
            backgroundColor: 'rgba(71, 85, 105, 0.3)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            borderRadius: '16px',
            p: 2,
          }}
        >
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type message"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            variant="standard"
            InputProps={{
              disableUnderline: true,
            }}
            sx={{
              '& .MuiInputBase-input': {
                color: '#ffffff',
                fontSize: '0.875rem',
                '&::placeholder': {
                  color: 'rgba(255, 255, 255, 0.5)',
                  opacity: 1,
                },
              },
            }}
          />
          <IconButton
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            sx={{
              color: newMessage.trim() ? '#6366f1' : 'rgba(255, 255, 255, 0.3)',
              '&:hover': {
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
              },
              '&.Mui-disabled': {
                color: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          >
            <SendIcon />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
}

import { useSearchParams } from 'react-router-dom';
import { useRef, useState, useEffect } from 'react';
// @mui
import { Send as SendOutlinedIcon } from '@mui/icons-material';
import InsertPhotoOutlinedIcon from '@mui/icons-material/InsertPhotoOutlined';
import KeyboardVoiceOutlinedIcon from '@mui/icons-material/KeyboardVoiceOutlined';
import { Box, Paper, Avatar, TextField, IconButton, Typography } from '@mui/material';
// components
import Logo from 'src/components/shared/logo';
import Scrollbar from 'src/components/shared/scrollbar';
import ConfirmationModal from './confirmation-modal';
import DoctorRecommendation from './doctor-recommendation';
import { Doctor, ChatMessage, ChatViewProps } from './type';

// ----------------------------------------------------------------------

export default function ChatView({ initialMessage = '' }: ChatViewProps) {
  const [searchParams] = useSearchParams();
  const messageFromUrl = searchParams.get('message') || initialMessage;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: "They come and go. But when they hit, they're pretty intense.",
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '2',
      text: "Sorry to hear that. Let's get to the bottom of it. Are the headaches constant, or do they come and go?",
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '3',
      text: "They come and go. But when they hit, they're pretty intense.",
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '4',
      text: 'Okay, understood. Where exactly do you feel the pain? Is it in one specific area or all over your head?',
      isUser: true,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '5',
      text: "They come and go. But when they hit, they're pretty intense.",
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
    },
    {
      id: '6',
      text: 'Based on your symptoms — throbbing pain around the temples and eyes, nausea, and light sensitivity — it sounds like you could be experiencing migraines. I recommend consulting a Neurologist to further assess and manage.',
      isUser: false,
      timestamp: '2:03 PM, 15 Nov',
      isDoctorRecommendation: true,
    },
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Set initial message from URL when component mounts
  useEffect(() => {
    if (messageFromUrl) {
      setNewMessage(messageFromUrl);
    }
  }, [messageFromUrl]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message: ChatMessage = {
        id: Date.now().toString(),
        text: newMessage,
        isUser: true,
        timestamp: new Date().toLocaleString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
          day: 'numeric',
          month: 'short',
        }),
      };
      setMessages((prev) => [...prev, message]);
      setNewMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleDoctorSelect = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedDoctor(null);
  };

  const handleConfirm = () => {
    // Handle confirmation logic here
    console.log('Doctor selected:', selectedDoctor);
    setIsModalOpen(false);
    setSelectedDoctor(null);
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Messages Container - 85% of screen height */}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        <Scrollbar sx={{ height: '75vh', px: 4 }}>
          {messages.map((message) => (
            <Box key={message.id}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: message.isUser ? 'row-reverse' : 'row',
                  alignItems: 'flex-start',
                  gap: 2,
                }}
              >
                {/* Avatar */}
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    backgroundColor: message.isUser ? '#6366f1' : 'transparent',
                    fontSize: '0.875rem',
                  }}
                >
                  {message.isUser ? 'U' : <Logo sx={{ width: 24, height: 24 }} />}
                </Avatar>

                {/* Message Content */}
                <Box
                  sx={{
                    maxWidth: '70%',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                  }}
                >
                  {/* Timestamp */}
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: message.isUser ? 'rgba(255, 255, 255, 0.7)' : 'rgba(255, 255, 255, 1)',
                      textAlign: message.isUser ? 'right' : 'left',
                    }}
                  >
                    {message.timestamp}
                  </Typography>

                  {/* Message Bubble */}
                  <Paper
                    sx={{
                      py: 2,
                      background: 'transparent',
                    }}
                  >
                    <Typography
                      fontSize={18}
                      sx={{
                        color: message.isUser ? 'rgba(255,255,255,0.7)' : '#ffffff',
                        lineHeight: 1.5,
                      }}
                    >
                      {message.text}
                    </Typography>
                  </Paper>
                </Box>
              </Box>

              {/* Doctor Recommendation */}
              {message.isDoctorRecommendation && (
                <DoctorRecommendation onDoctorSelect={handleDoctorSelect} />
              )}
            </Box>
          ))}

          {/* Invisible element to scroll to */}
          <div ref={messagesEndRef} />
        </Scrollbar>
      </Box>

      {/* Input Area - 15% of screen height - Fixed at bottom */}
      <Box
        sx={{
          height: '15vh',
          p: 3,
          borderTop: '1px solid rgba(255, 255, 255, 0.1)',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-end',
            backgroundColor: '#1C1E3C',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(148, 163, 184, 0.2)',
            borderRadius: '16px',
            p: 2,
            width: '100%',
          }}
        >
          <IconButton
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            sx={{
              color: 'rgba(255, 255, 255, 1)',
            }}
          >
            <KeyboardVoiceOutlinedIcon />
          </IconButton>
          <IconButton
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            sx={{
              color: 'rgba(255, 255, 255, 1)',
            }}
          >
            <InsertPhotoOutlinedIcon />
          </IconButton>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            placeholder="Type message"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            variant="standard"
            InputProps={{
              disableUnderline: true,
            }}
            sx={{
              '& .MuiInputBase-input': {
                color: '#ffffff',
                fontSize: '0.875rem',
                mb: 1,
                '&::placeholder': {
                  color: 'rgba(255, 255, 255, 0.5)',
                  opacity: 1,
                },
              },
            }}
          />
          <IconButton
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            sx={{
              color: newMessage.trim() ? 'rgba(255, 255, 255, 1)' : 'rgba(255, 255, 255, 0.3)',
              '&:hover': {
                backgroundColor: '#1C1E3C',
              },
              '&.Mui-disabled': {
                color: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          >
            <SendOutlinedIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Confirmation Modal */}
      <ConfirmationModal
        open={isModalOpen}
        onClose={handleModalClose}
        doctor={selectedDoctor}
        onConfirm={handleConfirm}
      />
    </Box>
  );
}

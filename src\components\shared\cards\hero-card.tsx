import { ReactNode } from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';

interface HeroCardProps {
  title: string;
  subtitle?: string;
  logo?: ReactNode;
  gradient?: string;
  size?: 'small' | 'medium' | 'large';
  sx?: object;
}

export default function HeroCard({ 
  title, 
  subtitle, 
  logo, 
  gradient = 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
  size = 'large',
  sx = {} 
}: HeroCardProps) {
  return (
    <Card
      sx={{
        background: gradient,
        borderRadius: '16px',
        height: '100%',
        minHeight: size === 'large' ? '280px' : '140px',
        position: 'relative',
        overflow: 'hidden',
        border: 'none',
        boxShadow: '0 8px 32px rgba(99, 102, 241, 0.3)',
        ...sx,
      }}
    >
      <CardContent 
        sx={{ 
          p: 3, 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column', 
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2,
        }}
      >
        {logo && (
          <Box sx={{ mb: 2, opacity: 0.9 }}>
            {logo}
          </Box>
        )}
        
        <Typography
          variant="h4"
          sx={{
            color: '#ffffff',
            fontWeight: 700,
            fontSize: size === 'large' ? '2rem' : '1.5rem',
            lineHeight: 1.3,
            textAlign: 'center',
            textShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
          }}
        >
          {title}
        </Typography>
        
        {subtitle && (
          <Typography
            variant="body1"
            sx={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '1rem',
              fontWeight: 400,
              mt: 1,
              textAlign: 'center',
            }}
          >
            {subtitle}
          </Typography>
        )}
      </CardContent>
      
      {/* Decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: -20,
          right: -20,
          width: 100,
          height: 100,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.1)',
          zIndex: 1,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -30,
          left: -30,
          width: 80,
          height: 80,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.05)',
          zIndex: 1,
        }}
      />
    </Card>
  );
}

import { ReactNode } from 'react';
import { Card, CardContent, Typography, Box, Avatar, AvatarGroup } from '@mui/material';
import { alpha } from '@mui/material/styles';

interface StatsCardProps {
  value: string;
  label: string;
  icon?: ReactNode;
  avatars?: string[];
  variant?: 'primary' | 'secondary';
  sx?: object;
}

export default function StatsCard({ 
  value, 
  label, 
  icon, 
  avatars, 
  variant = 'primary',
  sx = {} 
}: StatsCardProps) {
  return (
    <Card
      sx={{
        background: 'linear-gradient(135deg, #2a2d3a 0%, #1e2028 100%)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        height: '100%',
        minHeight: '140px',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: variant === 'primary' 
            ? 'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%)'
            : 'linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.05) 100%)',
          pointerEvents: 'none',
        },
        ...sx,
      }}
    >
      <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
        <Box>
          <Typography
            variant="h3"
            sx={{
              color: '#ffffff',
              fontWeight: 700,
              fontSize: '2.5rem',
              lineHeight: 1.2,
              mb: 0.5,
            }}
          >
            {value}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '0.875rem',
              fontWeight: 400,
              textTransform: 'capitalize',
            }}
          >
            {label}
          </Typography>
        </Box>
        
        {avatars && avatars.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <AvatarGroup 
              max={4} 
              sx={{
                '& .MuiAvatar-root': {
                  width: 32,
                  height: 32,
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  fontSize: '0.75rem',
                },
                '& .MuiAvatarGroup-avatar': {
                  backgroundColor: alpha('#6366f1', 0.8),
                  color: '#ffffff',
                  fontSize: '0.75rem',
                },
              }}
            >
              {avatars.map((avatar, index) => (
                <Avatar
                  key={index}
                  src={avatar}
                  sx={{
                    width: 32,
                    height: 32,
                  }}
                />
              ))}
            </AvatarGroup>
          </Box>
        )}
        
        {icon && (
          <Box sx={{ position: 'absolute', top: 16, right: 16, opacity: 0.3 }}>
            {icon}
          </Box>
        )}
      </CardContent>
    </Card>
  );
}

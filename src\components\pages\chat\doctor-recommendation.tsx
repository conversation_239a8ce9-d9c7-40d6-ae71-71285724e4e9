import { useState } from 'react';
import { Box, Card, Chip, Rating, Typography, CardContent } from '@mui/material';
import { Doctor } from './type';

interface DoctorRecommendationProps {
  onDoctorSelect: (doctor: Doctor) => void;
}

const mockDoctors: Doctor[] = [
  {
    id: '1',
    name: '<PERSON>',
    specialty: 'Neurologist',
    rating: 3.0,
    experience: 'Next Visit: 25 MAY',
    countries: 'US, German, Spain',
    image: 'https://via.placeholder.com/120x120/475569/ffffff?text=CW',
    badge: 'Good',
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    specialty: 'Neurologist',
    rating: 3.0,
    experience: 'Next Visit: 25 MAY',
    countries: 'US, German, Spain',
    image: 'https://via.placeholder.com/120x120/475569/ffffff?text=DR',
    badge: 'Best',
  },
  {
    id: '3',
    name: '<PERSON>',
    specialty: 'Neurologist',
    rating: 3.0,
    experience: 'Next Visit: 25 MAY',
    countries: 'US, German, Spain',
    image: 'https://via.placeholder.com/120x120/475569/ffffff?text=MM',
    badge: 'US',
  },
];

export default function DoctorRecommendation({ onDoctorSelect }: DoctorRecommendationProps) {
  const [hoveredDoctor, setHoveredDoctor] = useState<string | null>(null);

  return (
    <Box
      sx={{
        display: 'flex',
        gap: 3,
        justifyContent: 'center',
        alignItems: 'flex-end',
        mt: 4,
        mb: 3,
        px: 2,
      }}
    >
      {mockDoctors.map((doctor, index) => (
        <Card
          key={doctor.id}
          onClick={() => onDoctorSelect(doctor)}
          onMouseEnter={() => setHoveredDoctor(doctor.id)}
          onMouseLeave={() => setHoveredDoctor(null)}
          sx={{
            width: index === 1 || hoveredDoctor === doctor.id ? 200 : 180,
            height: index === 1 || hoveredDoctor === doctor.id ? 280 : 260,
            backgroundColor: 'rgba(71, 85, 105, 0.4)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(148, 163, 184, 0.3)',
            borderRadius: '16px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            transform:
              index === 1 || hoveredDoctor === doctor.id
                ? 'translateY(-10px) scale(1.05)'
                : 'translateY(0) scale(1)',
            zIndex: index === 1 || hoveredDoctor === doctor.id ? 10 : 1,
            position: 'relative',
            '&:hover': {
              backgroundColor: 'rgba(71, 85, 105, 0.6)',
              border: '1px solid rgba(148, 163, 184, 0.5)',
            },
          }}
        >
          <CardContent
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              height: '100%',
              position: 'relative',
            }}
          >
            {/* Badge */}
            <Chip
              label={doctor.badge}
              size="small"
              sx={{
                position: 'absolute',
                top: 12,
                right: 12,
                backgroundColor:
                  doctor.badge === 'Best'
                    ? 'rgba(34, 197, 94, 0.2)'
                    : doctor.badge === 'Good'
                    ? 'rgba(59, 130, 246, 0.2)'
                    : 'rgba(168, 85, 247, 0.2)',
                color:
                  doctor.badge === 'Best'
                    ? '#22c55e'
                    : doctor.badge === 'Good'
                    ? '#3b82f6'
                    : '#a855f7',
                border: `1px solid ${
                  doctor.badge === 'Best'
                    ? '#22c55e'
                    : doctor.badge === 'Good'
                    ? '#3b82f6'
                    : '#a855f7'
                }`,
                fontSize: '0.75rem',
                height: '24px',
              }}
            />

            {/* Doctor Image */}
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(148, 163, 184, 0.3)',
                mb: 2,
                mt: 3,
                backgroundImage: `url(${doctor.image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                border: '2px solid rgba(255, 255, 255, 0.1)',
              }}
            />

            {/* Doctor Name */}
            <Typography
              variant="h6"
              sx={{
                color: '#ffffff',
                fontSize: '1rem',
                fontWeight: 600,
                textAlign: 'center',
                mb: 0.5,
                lineHeight: 1.2,
              }}
            >
              {doctor.name}
            </Typography>

            {/* Countries */}
            <Typography
              variant="caption"
              sx={{
                color: 'rgba(255, 255, 255, 0.6)',
                fontSize: '0.75rem',
                textAlign: 'center',
                mb: 2,
              }}
            >
              Licensed Countries: {doctor.countries}
            </Typography>

            {/* Rating */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mb: 1,
              }}
            >
              <Rating
                value={doctor.rating}
                readOnly
                size="small"
                sx={{
                  '& .MuiRating-iconFilled': {
                    color: '#fbbf24',
                  },
                  '& .MuiRating-iconEmpty': {
                    color: 'rgba(255, 255, 255, 0.3)',
                  },
                }}
              />
              <Typography
                variant="caption"
                sx={{
                  color: '#ffffff',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                }}
              >
                {doctor.rating.toFixed(1)}
              </Typography>
            </Box>

            {/* Next Visit */}
            <Typography
              variant="caption"
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '0.75rem',
                textAlign: 'center',
              }}
            >
              {doctor.experience}
            </Typography>
          </CardContent>
        </Card>
      ))}
    </Box>
  );
}

import { Box, Chip, Card, Rating, Typography, CardContent } from '@mui/material';
import { Doctor } from './type';

interface DoctorRecommendationProps {
  onDoctorSelect: (doctor: Doctor) => void;
}

const mockDoctors: Doctor[] = [
  {
    id: '1',
    name: '<PERSON>',
    specialty: 'Neurologist',
    rating: 3.0,
    experience: 'Next Visit: 25 MAY',
    countries: 'US, German, Spain',
    image: '/assets/images/sample/image1.png',
    badge: 'Good',
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    specialty: 'Neurologist',
    rating: 3.0,
    experience: 'Next Visit: 25 MAY',
    countries: 'US, German, Spain',
    image: '/assets/images/sample/image.png',
    badge: 'Best',
  },
  {
    id: '3',
    name: '<PERSON>',
    specialty: 'Neurologist',
    rating: 3.0,
    experience: 'Next Visit: 25 MAY',
    countries: 'US, German, Spain',
    image: '/assets/images/sample/image_2.png',
    badge: 'US',
  },
];

export default function DoctorRecommendation({ onDoctorSelect }: DoctorRecommendationProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 3,
        justifyContent: 'center',
        alignItems: 'flex-end',
        mt: 4,
        mb: 3,
        px: 2,
      }}
    >
      {mockDoctors.map((doctor, index) => (
        <Card
          key={doctor.id}
          onClick={() => onDoctorSelect(doctor)}
          sx={{
            borderRadius: '20px',
            border: '0.856px solid inside',
            borderColor: 'rgba(255,255,255,0.08)',
            position: 'relative',
            width: '100%',
            height: 'fit-content',
            boxShadow: 'none',
            overflow: 'hidden',
            '&:before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: -1,
              width: '100%',
              height: '100%',
              background: `radial-gradient(83.04% 114.3% at 0.2% 100%, rgba(0, 0, 0, 0.00) 27.6%, #222344 81.88%), url(${doctor.image}) lightgray -319.864px 110.632px / 226.718% 100% no-repeat, radial-gradient(217.24% 242.87% at 217.75% 107.58%, rgba(127, 139, 210, 0.30) 0%, rgba(89, 106, 197, 0.00) 100%),#191934`,
              backgroundPosition: 'bottom',
              backgroundSize: '100% auto',
              backgroundRepeat: 'no-repeat',
              backgroundColor: '#191934',
              backgroundBlendMode: 'overlay, normal',
              borderRadius: '20px',
            },
            '&:after': {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '100%',
              height: '100%',
              zIndex: -1,
              pointerEvents: 'none',
              background: 'linear-gradient(270deg, rgba(58,66,216,0.18) 0%, rgba(58,66,216,0) 50%)',
              borderTopRightRadius: '20px',
              borderBottomRightRadius: '20px',
            },
          }}
        >
          <CardContent
            sx={{
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              height: '100%',
              position: 'relative',
            }}
          >
            {/* Badge */}
            <Chip
              label={doctor.badge}
              size="small"
              sx={{
                position: 'absolute',
                top: 12,
                right: 12,
                backgroundColor: '#fff',
                color: '#000',
                fontSize: '0.75rem',
                height: '24px',
              }}
            />

            {/* Doctor Image */}
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(148, 163, 184, 0.3)',
                mb: 2,
                mt: 3,
                backgroundImage: `url(${doctor.image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                border: '2px solid rgba(255, 255, 255, 0.1)',
              }}
            />

            {/* Doctor Name */}
            <Typography
              variant="h6"
              sx={{
                color: '#ffffff',
                fontSize: '1rem',
                fontWeight: 600,
                textAlign: 'center',
                mb: 0.5,
                lineHeight: 1.2,
              }}
            >
              {doctor.name}
            </Typography>

            {/* Countries */}
            <Typography
              variant="caption"
              sx={{
                color: 'rgba(255, 255, 255, 0.6)',
                fontSize: '0.75rem',
                textAlign: 'center',
                mb: 2,
              }}
            >
              Licensed Countries: {doctor.countries}
            </Typography>

            {/* Rating */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mb: 1,
              }}
            >
              <Rating
                value={doctor.rating}
                readOnly
                size="small"
                sx={{
                  '& .MuiRating-iconFilled': {
                    color: '#fbbf24',
                  },
                  '& .MuiRating-iconEmpty': {
                    color: 'rgba(255, 255, 255, 0.3)',
                  },
                }}
              />
              <Typography
                variant="caption"
                sx={{
                  color: '#ffffff',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                }}
              >
                {doctor.rating.toFixed(1)}
              </Typography>
            </Box>

            {/* Next Visit */}
            <Typography
              variant="caption"
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '0.75rem',
                textAlign: 'center',
              }}
            >
              {doctor.experience}
            </Typography>
          </CardContent>
        </Card>
      ))}
    </Box>
  );
}
